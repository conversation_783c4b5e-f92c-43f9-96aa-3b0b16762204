/* printer.wxss */
.container {
  background-color: #f5f5f5;
  height: 100vh; /* 固定高度，防止拖动 */
  padding-bottom: 140rpx; /* 调整底部间距适应新菜单高度 */
  box-sizing: border-box;
  width: 100%; /* 确保全宽 */

}

/* 自定义导航栏 */
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  z-index: 999;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.navbar-content {
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  position: relative;
  box-sizing: border-box;
  justify-content: space-between; /* 分散对齐 */
}

.navbar-logo {
  width: 220rpx;
  height: 60rpx;
  margin-right: 15rpx;
  flex-shrink: 0;
}

.navbar-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  position: absolute;
  left: 50%;
  transform: translateX(-50%); /* 绝对居中 */
  text-align: center;
  width: 100%;
}

/* 页面内容区域 */
.page-content {
  padding: 0rpx 15rpx 15rpx 15rpx; /* 恢复正常的内边距 */
  box-sizing: border-box;
  margin-top: -40rpx; /* 确保没有额外的上边距 */
  width: 100%; /* 确保左右满屏 */
}

/* 隐藏的Canvas */
.hidden-canvas {
  position: fixed;
  top: -9999px;
  left: -9999px;
}

/* 打印机状态栏 */
.printer-status {
  margin-bottom: 20rpx;
  padding: 18rpx 20rpx;
  border-radius: 14rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
  box-sizing: border-box;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

/* 状态栏强调条 */
.status-accent {
  width: 8rpx;
  height: 100%;
  border-radius: 6rpx;
}

.printer-status.connected .status-accent { background-color: #34C759; }
.printer-status.printing .status-accent { background-color: #007AFF; }
.printer-status.error .status-accent { background-color: #E53935; }
.printer-status.disconnected .status-accent { background-color: #FFB020; }
.printer-status.scanning .status-accent { background-color: #5AC8FA; }
.printer-status.connecting .status-accent { background-color: #34C759; }
.printer-status.disconnecting .status-accent { background-color: #FF3B30; }

.printer-status.disconnected {
  background-color: #FFF3CD;
  border: 1px solid #FFEAA7;
}

.printer-status.error {
  background-color: #F8D7DA;
  border: 1px solid #F5C6CB;
}

.printer-status.connected {
  background-color: #D4EDDA;
  border: 1px solid #C3E6CB;
}

.printer-status.printing {
  background-color: #CCE5FF;
  border: 1px solid #99D3FF;
}

.printer-status.scanning {
  background-color: #E1F5FE;
  border: 1px solid #B3E5FC;
}

.printer-status.connecting {
  background-color: #E8F5E8;
  border: 1px solid #C8E6C9;
}

.printer-status.disconnecting {
  background-color: #FFEBEE;
  border: 1px solid #FFCDD2;
}

.status-text {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  font-weight: 500;
  gap: 10rpx;
  flex: 1;
}

.status-icon {
  margin-right: 12rpx;
  font-size: 32rpx;
}

/* 错误信息显示 */
.error-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.error-message {
  font-weight: 500;
  color: #721c24;
}

.warning-message {
  font-weight: 500;
  color: #856404;
  font-size: 26rpx;
}

.error-code {
  font-size: 24rpx;
  color: #856404;
  margin-top: 4rpx;
}

/* 停止打印按钮 */
.stop-print-btn {
  margin-left: auto;
  padding: 8rpx 16rpx;
  background-color: #dc3545;
  color: white;
  border-radius: 6rpx;
  font-size: 24rpx;
  font-weight: 500;
}

/* 状态栏操作按钮容器 */
.status-actions {
  display: flex;
  gap: 12rpx;
  margin-left: auto;
}

/* 刷新耗材按钮 */
.refresh-material-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 12rpx;
  background-color: transparent;
  color: #FF9500;
  border: 1px solid #FF9500;
  border-radius: 8rpx;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 1.2;
  transition: all 0.2s;
  min-width: 60rpx;
  min-height: 50rpx;
}

.refresh-material-btn text {
  display: block;
  line-height: 1;
}

.refresh-material-btn:active {
  background-color: #FF9500;
  color: white;
}

/* 更改设备按钮 */
.change-device-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 12rpx;
  background-color: transparent;
  color: #007AFF;
  border: 1px solid #007AFF;
  border-radius: 999rpx;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 1.2;
  transition: all 0.2s;
  min-width: 60rpx;
  min-height: 50rpx;

}

.change-device-btn text {
  display: block;
  line-height: 1;
}

.change-device-btn:active {
  background-color: #007AFF;
  color: white;
}

/* 断开连接按钮 */
.disconnect-device-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8rpx 12rpx;
  background-color: transparent;
  color: #FF3B30;
  border: 1px solid #FF3B30;
  border-radius: 999rpx;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 1.2;
  transition: all 0.2s;
  min-width: 60rpx;
  min-height: 50rpx;
}

.disconnect-device-btn text {
  display: block;
  line-height: 1;
}

.disconnect-device-btn:active {
  background-color: #FF3B30;
  color: white;
}

/* 未连接在状态栏的“去连接”按钮 */
.connect-now-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  background-color: #fff;
  color: #007AFF;
  border: 1px solid #007AFF;
  border-radius: 999rpx;
  font-size: 24rpx;
  font-weight: 500;
}
.connect-now-btn.disabled { opacity: 0.6; }
.connect-now-btn:active { background-color: #007AFF; color: #fff; }

/* 底部主按钮状态色：未连接=灰，已连接=绿，打印中=黄 */
.menu-item.main-action.ready { background-color: #28a745; }
.menu-item.main-action.disabled { background-color: #cccccc; color: #666; }
.menu-item.main-action.printing { background-color: #f0ad4e; }

/* 首次进入连接弹窗按钮样式 */
.modal-actions { display: flex; justify-content: flex-end; gap: 20rpx; margin-top: 20rpx; }
.btn { padding: 14rpx 26rpx; border-radius: 8rpx; font-size: 28rpx; }
.btn.weak { background: #f5f5f5; color: #666; }
.btn.primary { background: #28a745; color: #fff; }
.btn.disabled { opacity: 0.6; }


.ad-tip {
  display: block;
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #cfcfcf;
  text-align: center;
}

/* 通用区块样式 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between; /* 使标题和问号分别靠左靠右 */
}

/* 帮助图标样式 */
.help-icon {
  display: inline-block;
  width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  text-align: center;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #666;
  font-size: 24rpx;
  margin-right: 10rpx;
  font-weight: normal;
}

/* 标签规格列表 */
.template-section {
  margin-bottom: 20rpx; /* 减少间距 */
  background-color: white;
  padding: 15rpx; /* 减少内边距 */
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  position: relative; /* 为气泡提示定位 */
  /* align-items: center; */
}

/* 模版帮助提示气泡 */
.template-help-tooltip {
  position: absolute;
  top: 85rpx;
  right: 25rpx; /* 调整位置与标题旁边的问号对齐 */
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  z-index: 101; /* 高于tabs，防止遮挡 */
  max-width: 400rpx;
  opacity: 0;
  transform: translateY(-10rpx);
  transition: opacity 0.3s, transform 0.3s;
  pointer-events: none; /* 防止气泡阻挡点击 */
}

/* 气泡小三角 */
.template-help-tooltip:after {
  content: '';
  position: absolute;
  top: -10rpx;
  right: 10rpx; /* 调整小三角位置与问号图标中心对齐 */
  border-left: 10rpx solid transparent;
  border-right: 10rpx solid transparent;
  border-bottom: 10rpx solid rgba(0, 0, 0, 0.7);
}

/* 标签页（Tag）样式：占用标题位置，不遮挡问号 */
.section-title { display: flex; align-items: center; justify-content: space-between; }
.section-title .help-icon { margin-left: 12rpx; flex-shrink: 0; }

/* 当前模板信息样式 */
.current-template-info {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
  margin-left: 16rpx;
  display: flex;
  align-items: center;
}

.template-category {
  color: #666;
}

.template-separator {
  color: #ccc;
  margin: 0 8rpx;
}

.template-name {
  color: #999;
}

/* 可点击样式 */
.clickable {
  cursor: pointer;
  transition: color 0.2s ease;
}

.clickable:hover {
  color: #007AFF;
  text-decoration: underline;
}

.tabs-scroll {
  flex: 1;
  margin-right: 16rpx;
  width: 100%;
  white-space: nowrap;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 60rpx;
  padding: 0 15rpx;
  font-size: 32rpx;
  color: #666;
  margin-right: 0;
  position: relative;
  flex-shrink: 0;
  transition: all 0.3s;
}

.tag-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4rpx;
  background-color: #007AFF;
  transform: scaleX(0);
  transition: transform 0.3s;
}

.tag-item.active {
  color: #007AFF;
  font-weight: 600;
}

.tag-item.active::after {
  transform: scaleX(0.8);
}

.tag-text {
  white-space: nowrap;
}


/* 分类标签页 */
.template-tabs {
  margin-top: 10rpx;
  margin-bottom: 10rpx;
  position: relative;
  z-index: 100;
}

/* Tab 容器样式 */
.tabs-container {
  display: flex;
  align-items: center;
  height: 80rpx;
  position: relative;
  width: 100%;
}

/* 滚动区域样式优化 */
.tabs-scroll {
  background-color: #fff;
  height: 82rpx;
  position: relative;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  display: inline-flex;
  align-items: center;
  height: 64rpx;
  padding: 0 24rpx;
  border-radius: 999rpx;
  background: #f5f5f5;
  color: #666;
  margin-right: 16rpx;
  font-size: 26rpx;
  border: 1px solid #e5e5e5;
  flex-shrink: 0;
}
.tab-item.active {
  background: #EAF4FF;
  color: #007AFF;
  border-color: #91d5ff;
  font-weight: 600;
}
.tab-text { white-space: nowrap; }


/* 显示状态 */
.template-help-tooltip.visible {
  opacity: 1;
  transform: translateY(0);
}

/* 隐藏状态 */
.template-help-tooltip.hidden {
  opacity: 0;
  transform: translateY(-10rpx);
}

/* 模版列表容器 */
.template-list-container {
  position: relative;
  width: 100%;
}

/* 模版列表包装器 */
.template-list-wrapper {
  display: flex;
  align-items: center;
  width: 100%;
}

/* 滚动指示器 */
.template-scroll-indicator {
  flex-shrink: 0;
  width: 8rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.template-scroll-indicator.left {
  margin-right: 15rpx;
}

.template-scroll-indicator.right {
  margin-left: 15rpx;
}

.template-scroll-indicator.visible {
  opacity: 1;
}

.template-scroll-indicator.hidden {
  opacity: 0;
}

/* 指示器竖条 */
.indicator-bar {
  width: 8rpx;
  height: 80rpx;
  background: linear-gradient(to bottom, #B3E5FC, #81D4FA);
  border-radius: 4rpx;
  box-shadow: 0 2rpx 8rpx rgba(179, 229, 252, 0.3);
}

/* 滚动提示文字 */
.template-scroll-hint {
  position: absolute;
  top: -50rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  z-index: 15;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.template-scroll-hint.visible {
  opacity: 1;
}

.template-scroll-hint.hidden {
  opacity: 0;
}

.hint-text {
  white-space: nowrap;
}

.template-list {
  white-space: nowrap;
  flex: 1;
  overflow-x: auto;
  scroll-behavior: smooth;
  display: flex;
  justify-content: flex-start;
  -webkit-overflow-scrolling: touch; /* 启用平滑滚动 */
  scrollbar-width: thin; /* 滚动条宽度 */
  scrollbar-color: #007AFF #f0f0f0; /* 滚动条颜色 */
}

.template-item {
  display: inline-block;
  width: 200rpx;
  margin-right: 20rpx;
  text-align: center;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 12rpx;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.template-item.selected {
  border-color: #007AFF;
  background-color: #EAF4FF;
  box-shadow: 0 6rpx 16rpx rgba(0,122,255,0.18);
}

.template-preview {
  width: 160rpx;
  height: 100rpx;
  margin: 0 auto 15rpx;
  border-radius: 8rpx;
  overflow: hidden;
  background-color: white;
}

.template-image {
  width: 100%;
  height: 100%;
}

.template-name {
  font-size: 24rpx;
  color: #334155;
  font-weight: 600;
}

/* 预览图区域 */
.preview-section {
  margin-bottom: 20rpx; /* 减少间距 */
  background-color: white;
  padding: 15rpx; /* 减少内边距 */
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  box-sizing: border-box;
}

.preview-container {

  text-align: center;
  min-height: 150rpx; /* 减少高度 */
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-image {
  background-color: #999;
  max-width: 100%;
  max-height: 300rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  padding: 10rpx 0; /* 上下留白 */
}

.preview-placeholder {
  color: #999;
  font-size: 28rpx;
}

/* 标签内容输入 */
.content-section {
  margin-bottom: 20rpx; /* 减少间距 */
  background-color: white;
  padding: 15rpx; /* 减少内边距 */
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  box-sizing: border-box;
}

.content-form {
  margin-top: 15rpx; /* 减少间距 */
}

.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx; /* 减少间距 */
}

.form-label {
  width: 170rpx;
  font-size: 28rpx;
  color: #333;
  flex-shrink: 0;
}

.input-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  padding-right: 80rpx; /* 为字符计数留出空间 */
  border: 1px solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.char-count {
  position: absolute;
  right: 15rpx;
  font-size: 24rpx;
  color: #999;
  pointer-events: none;
  white-space: nowrap;
  transition: color 0.2s ease;
}

.char-count.warning {
  color: #FF9500;
  font-weight: 500;
}

.form-picker {
  flex: 1;
  height: 80rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  background-color: #fff;
}

.picker-text {
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}

/* 日期控制器 */
.date-control {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.date-control .form-picker {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 8rpx;
}

.date-btn, .clear-btn {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  font-size: 24rpx;
  color: #666;
  transition: background-color 0.2s;
}

.date-btn:active, .clear-btn:active {
  background-color: #e0e0e0;
}

.date-btn {
  background-color: #e8f4fd;
  color: #1890ff;
  border-color: #91d5ff;
}

.date-btn:active {
  background-color: #bae7ff;
}

/* DATE_TIME 日期时间容器 */
.datetime-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.datetime-row {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.time-control {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.time-control .form-picker {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 8rpx;
}

.time-picker .picker-text {
  color: #333;
}

/* 打印份数控制器 */
.copies-control {
  flex: 1;
  display: flex;
  align-items: center;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  background-color: #fff;
  overflow: hidden;
}

.copies-btn {
  width: 60rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  font-size: 32rpx;
  font-weight: bold;
  color: #666;
  border-right: 1px solid #ddd;
  transition: background-color 0.2s;
}

.copies-btn:last-child {
  border-right: none;
  border-left: 1px solid #ddd;
}

/* 动态输入扩展样式 */
.form-input.small {
  width: 120rpx;
  text-align: center;
}
.checkbox-group-inline {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx 20rpx;
}
.checkbox-item {
  display: flex;
  align-items: center;
  padding: 8rpx 12rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  background: #fff;
}
.checkbox-icon {
  width: 32rpx;
  display: inline-block;
  text-align: center;
  margin-right: 8rpx;
}

/* OPTION 下拉选择样式 */
.option-container {
  flex: 1;
}

.option-row {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.option-picker {
  flex-shrink: 0;
  min-width: 200rpx;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  border: 1px solid #ddd;
  border-radius: 8rpx;
  background: #fff;
  min-height: 52rpx;
  font-size: 28rpx;
  color: #333;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
  margin-left: 16rpx;
}

/* 自定义输入框样式 */
.custom-input-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
}

.custom-input {
  flex: 1;
  height: 80rpx;
  padding: 0 20rpx;
  padding-right: 80rpx; /* 为字符计数留出空间 */
  border: 1px solid #ddd;
  border-radius: 8rpx;
  background: #fff;
  font-size: 28rpx;
  color: #333;
}

.custom-input-wrapper .char-count {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #999;
  pointer-events: none;
}


.copies-btn:active {
  background-color: #e0e0e0;
}

.copies-input {
  flex: 1;
  height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border: none;
  background-color: #fff;
}

/* 底部菜单栏 */
.bottom-menu {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx; /* 适当增加高度确保按钮显示完整 */
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: space-around;
  box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.1);
  padding: 15rpx 20rpx; /* 适当增加垂直内边距 */
  z-index: 100; /* 确保在最上层 */
  box-sizing: border-box;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 90rpx; /* 适当增加高度 */
  border-radius: 8rpx;
  transition: all 0.3s ease;
  position: relative;
  min-width: 0; /* 防止flex项目溢出 */
  padding: 5rpx; /* 添加内边距 */
  box-sizing: border-box;
}

.menu-item.main-action {
  background-color: #007AFF;
  color: white;
  flex: 2;
  /* border-radius: 999rpx; */
}

.menu-item.main-action.printing {
  background-color: #f0ad4e;
}

.menu-item.main-action.connecting {
  background-color: #28a745;
  opacity: 0.8;
}

.menu-icon {
  font-size: 30rpx;
  margin-bottom: 4rpx;
  line-height: 1;
}

.menu-text {
  font-size: 22rpx; /* 恢复合适的字体大小 */
  font-weight: 500;
  line-height: 2;
  text-align: center;
  white-space: nowrap; /* 防止文字换行 */
  overflow: hidden;
  text-overflow: ellipsis; /* 超长文字显示省略号 */
  max-width: 100%; /* 确保不超出容器 */
}

.menu-text-center {
  font-size: 24rpx;
  font-weight: 600;
  line-height: 0.75;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 移除loading样式，状态信息已移至状态栏 */

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.55);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  width: 80%;
  max-width: 600rpx;
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx;
  max-height: 70%;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #eee;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  width: 56rpx;
  height: 56rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 40rpx;
  color: #999;
  background-color: transparent;
  border-radius: 8rpx;
}

/* 扫描状态提示 */
.scanning-status {
  font-size: 24rpx;
  color: #007AFF;
  margin-right: 20rpx;
}

/* 设备列表 */
.device-list {
  max-height: 400rpx;
  overflow-y: auto;
}

.device-item {
  padding: 30rpx 20rpx;
  border-bottom: 1px solid #eee;
  font-size: 28rpx;
  color: #333;
  transition: background-color 0.2s;
}

.device-item:last-child {
  border-bottom: none;
}

.device-item.current-device {
  background-color: #E3F2FD;
  border-left: 4rpx solid #007AFF;
}

.device-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.device-name {
  font-weight: 500;
  flex: 1;
}

.device-status {
  font-size: 24rpx;
  color: #007AFF;
  font-weight: 500;
  padding: 4rpx 8rpx;
  background-color: rgba(0, 122, 255, 0.1);
  border-radius: 4rpx;
}

/* 设备列表提示 */
.no-device-tip, .scanning-tip {
  padding: 60rpx 20rpx;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.scanning-tip {
  color: #007AFF;
}

/* 联系我们弹窗 */
.contact-modal {
  width: 90%;
}

.contact-info {
  line-height: 1.6;
}

.contact-item {
  margin-bottom: 25rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.contact-label {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
}

.contact-value {
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}

/* 脉冲动画效果 */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

.device-item.connecting-device {
  background-color: #FFF8E1;
  border-left: 4rpx solid #FFA000;
}

.device-item.disabled {
  opacity: 0.6;
  pointer-events: none;
  cursor: not-allowed;
}

.device-status.connecting {
  color: #FFA000;
  background-color: rgba(255, 160, 0, 0.1);
  animation: pulse 1.5s infinite;
}

/* 自定义模板项样式 */
.custom-template-item {
  background-color: #fafafa;
  border: 2rpx dashed #cccccc;
}

.custom-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.custom-preview-text {
  font-size: 26rpx;
  font-weight: normal;
  color: #666666;
}

/* 广告弹窗样式 */
.ad-modal {
  background-color: transparent;
  position: relative;
  width: 90%;
  max-width: 650rpx;
  padding: 30rpx;
}

.image-close {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.3);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  z-index: 10;
} 

.ad-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.ad-image {
  width: 100%;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.ad-description {
  width: 100%;
  text-align: center;
  padding: 10rpx 0;
}

.ad-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.ad-text {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.ad-contact-btn {
  width: 80%;
  height: 80rpx;
  line-height: 50rpx;
  background-color: #007AFF;
  color: white;
  font-size: 28rpx;
  font-weight: bold;
  border-radius: 40rpx;
  margin: 0 auto;
}

/* 操作指引图片对话框样式 */
.operation-guide-modal {
  width: 85%;
  max-width: 700rpx;
  padding: 0;
}

.operation-guide-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-guide-image-wrapper {
  width: 100%;
  margin-bottom: 30rpx;
}

.operation-guide-image {
  width: 100%;
  border-radius: 8rpx;
}

.operation-guide-message {
  width: 100%;
  text-align: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  margin-bottom: 30rpx;
}

.operation-guide-message text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}